/**
 * Comprehensive Authentication Fix Test Script
 * 
 * This script tests the updated authentication flow where API functions
 * now fetch JWT tokens directly from Supabase sessions instead of
 * accepting tokens as parameters.
 * 
 * Usage:
 * 1. Open https://ai-nav-eosin.vercel.app in browser
 * 2. Log in with credentials: <EMAIL> / testtest
 * 3. Open browser console (F12)
 * 4. Copy and paste this entire script
 * 5. Run: testAuthenticationFix()
 */

// Test configuration
const TEST_CONFIG = {
  apiBaseUrl: 'https://ai-nav.onrender.com',
  testEntityId: 'test-entity-id', // Will be replaced with actual entity ID
  maxRetries: 3,
  retryDelay: 1000
};

// Helper function to wait
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to log test results
const logTest = (testName, success, details = '') => {
  const status = success ? '✅' : '❌';
  console.log(`${status} ${testName}${details ? ': ' + details : ''}`);
  return success;
};

// Helper function to get Supabase session
const getSupabaseSession = async () => {
  try {
    // Try to get the createSupabaseBrowserClient function
    const { createSupabaseBrowserClient } = await import('/src/lib/supabase/client.js');
    const supabase = createSupabaseBrowserClient();
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting Supabase session:', error);
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Error importing Supabase client:', error);
    return null;
  }
};

// Test 1: Verify Supabase session exists
const testSupabaseSession = async () => {
  console.log('\n🔍 Test 1: Verifying Supabase Session');
  
  const session = await getSupabaseSession();
  
  if (!session) {
    return logTest('Supabase Session', false, 'No active session found');
  }
  
  if (!session.access_token) {
    return logTest('Supabase Session', false, 'Session exists but no access token');
  }
  
  logTest('Supabase Session', true, `Token length: ${session.access_token.length}`);
  logTest('User Email', true, session.user?.email || 'No email');
  
  return true;
};

// Test 2: Test profile API calls
const testProfileAPIs = async () => {
  console.log('\n🔍 Test 2: Testing Profile API Calls');
  
  try {
    // Test getCompleteProfile (should work without token parameter)
    const response = await fetch('/api/test-profile', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ test: 'getCompleteProfile' })
    });
    
    if (response.ok) {
      return logTest('Profile API', true, 'getCompleteProfile works');
    } else {
      return logTest('Profile API', false, `Status: ${response.status}`);
    }
  } catch (error) {
    return logTest('Profile API', false, error.message);
  }
};

// Test 3: Test upvote functionality
const testUpvoteFunctionality = async () => {
  console.log('\n🔍 Test 3: Testing Upvote Functionality');
  
  try {
    // First, get some entities to test with
    const entitiesResponse = await fetch(`${TEST_CONFIG.apiBaseUrl}/entities?limit=1`);
    
    if (!entitiesResponse.ok) {
      return logTest('Upvote Test', false, 'Could not fetch entities for testing');
    }
    
    const entitiesData = await entitiesResponse.json();
    
    if (!entitiesData.data || entitiesData.data.length === 0) {
      return logTest('Upvote Test', false, 'No entities available for testing');
    }
    
    const testEntityId = entitiesData.data[0].id;
    logTest('Test Entity Found', true, `ID: ${testEntityId}`);
    
    // Test upvote (this should work with the new auth flow)
    // We'll simulate this by checking if the upvote button works on the page
    const upvoteButtons = document.querySelectorAll('[data-testid*="upvote"], button[aria-label*="upvote"], button[title*="upvote"]');
    
    if (upvoteButtons.length > 0) {
      return logTest('Upvote UI', true, `Found ${upvoteButtons.length} upvote buttons`);
    } else {
      return logTest('Upvote UI', false, 'No upvote buttons found on page');
    }
  } catch (error) {
    return logTest('Upvote Test', false, error.message);
  }
};

// Test 4: Test bookmark functionality
const testBookmarkFunctionality = async () => {
  console.log('\n🔍 Test 4: Testing Bookmark Functionality');
  
  try {
    // Look for bookmark buttons on the page
    const bookmarkButtons = document.querySelectorAll('[data-testid*="bookmark"], button[aria-label*="bookmark"], button[title*="bookmark"]');
    
    if (bookmarkButtons.length > 0) {
      return logTest('Bookmark UI', true, `Found ${bookmarkButtons.length} bookmark buttons`);
    } else {
      return logTest('Bookmark UI', false, 'No bookmark buttons found on page');
    }
  } catch (error) {
    return logTest('Bookmark Test', false, error.message);
  }
};

// Test 5: Test navigation to protected pages
const testProtectedPages = async () => {
  console.log('\n🔍 Test 5: Testing Protected Page Access');
  
  const protectedPages = [
    { name: 'Profile', url: '/profile' },
    { name: 'Submit', url: '/submit' },
    { name: 'Chat', url: '/chat' },
    { name: 'Admin', url: '/admin' }
  ];
  
  let allPassed = true;
  
  for (const page of protectedPages) {
    try {
      // Check if the page link exists and is accessible
      const link = document.querySelector(`a[href="${page.url}"]`);
      
      if (link) {
        logTest(`${page.name} Page Link`, true, 'Link found in navigation');
      } else {
        logTest(`${page.name} Page Link`, false, 'Link not found');
        allPassed = false;
      }
    } catch (error) {
      logTest(`${page.name} Page`, false, error.message);
      allPassed = false;
    }
  }
  
  return allPassed;
};

// Test 6: Test localStorage cleanup
const testLocalStorageCleanup = async () => {
  console.log('\n🔍 Test 6: Testing localStorage Cleanup');
  
  // Check that we're not storing JWT tokens in localStorage anymore
  const localStorageKeys = Object.keys(localStorage);
  const authKeys = localStorageKeys.filter(key => 
    key.includes('jwt') || 
    key.includes('token') || 
    key.includes('auth-token') ||
    (key.includes('auth') && !key.includes('supabase'))
  );
  
  if (authKeys.length === 0) {
    return logTest('localStorage Cleanup', true, 'No JWT tokens found in localStorage');
  } else {
    return logTest('localStorage Cleanup', false, `Found auth keys: ${authKeys.join(', ')}`);
  }
};

// Main test function
const testAuthenticationFix = async () => {
  console.log('🧪 Starting Comprehensive Authentication Fix Tests');
  console.log('================================================');
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0
  };
  
  const tests = [
    testSupabaseSession,
    testProfileAPIs,
    testUpvoteFunctionality,
    testBookmarkFunctionality,
    testProtectedPages,
    testLocalStorageCleanup
  ];
  
  for (const test of tests) {
    try {
      const result = await test();
      results.total++;
      
      if (result) {
        results.passed++;
      } else {
        results.failed++;
      }
      
      // Wait between tests
      await wait(500);
    } catch (error) {
      console.error('Test error:', error);
      results.total++;
      results.failed++;
    }
  }
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Total Tests: ${results.total}`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Authentication fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the issues above.');
  }
  
  return results;
};

// Instructions
console.log(`
🧪 Authentication Fix Test Script Loaded

To run the tests:
1. Make sure you're logged in to AI Navigator
2. Run: testAuthenticationFix()

Available functions:
- testAuthenticationFix() - Run all tests
- testSupabaseSession() - Test session only
- getSupabaseSession() - Get current session info
`);

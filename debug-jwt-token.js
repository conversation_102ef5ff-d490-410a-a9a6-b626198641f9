/**
 * Debug script to analyze Supabase JWT tokens
 * Run this in browser console when you have a session to see token structure
 */

// Function to decode JWT token (without verification)
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const header = JSON.parse(atob(parts[0]));
    const payload = JSON.parse(atob(parts[1]));
    
    return {
      header,
      payload,
      signature: parts[2]
    };
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

// Function to analyze current session token
async function analyzeCurrentToken() {
  // This assumes you're running in browser with Supabase client available
  if (typeof window === 'undefined') {
    console.log('This script should be run in the browser console');
    return;
  }
  
  try {
    // Try to get session from localStorage or sessionStorage
    const supabaseSession = localStorage.getItem('sb-qpkllizwchhppvqsnagl-auth-token');
    
    if (!supabaseSession) {
      console.log('No Supabase session found in localStorage');
      return;
    }
    
    const sessionData = JSON.parse(supabaseSession);
    const accessToken = sessionData.access_token;
    
    if (!accessToken) {
      console.log('No access token found in session');
      return;
    }
    
    console.log('=== JWT TOKEN ANALYSIS ===');
    console.log('Raw token (first 50 chars):', accessToken.substring(0, 50) + '...');
    
    const decoded = decodeJWT(accessToken);
    if (decoded) {
      console.log('\n=== JWT HEADER ===');
      console.log(JSON.stringify(decoded.header, null, 2));
      
      console.log('\n=== JWT PAYLOAD ===');
      console.log(JSON.stringify(decoded.payload, null, 2));
      
      console.log('\n=== TOKEN INFO ===');
      console.log('Issuer:', decoded.payload.iss);
      console.log('Subject (User ID):', decoded.payload.sub);
      console.log('Email:', decoded.payload.email);
      console.log('Role:', decoded.payload.role);
      console.log('Audience:', decoded.payload.aud);
      console.log('Expires:', new Date(decoded.payload.exp * 1000).toISOString());
      console.log('Issued:', new Date(decoded.payload.iat * 1000).toISOString());
      
      console.log('\n=== USER METADATA ===');
      console.log(JSON.stringify(decoded.payload.user_metadata, null, 2));
      
      console.log('\n=== APP METADATA ===');
      console.log(JSON.stringify(decoded.payload.app_metadata, null, 2));
    }
    
  } catch (error) {
    console.error('Error analyzing token:', error);
  }
}

// Function to test backend API call with detailed debugging
async function testBackendCall() {
  try {
    console.log('=== COMPREHENSIVE TOKEN DEBUG ===');

    // Check all possible localStorage keys
    const allKeys = Object.keys(localStorage).filter(key => key.includes('supabase') || key.includes('auth'));
    console.log('All auth-related localStorage keys:', allKeys);

    // Try different possible session storage keys
    const possibleKeys = [
      'sb-qpkllizwchhppvqsnagl-auth-token',
      'supabase.auth.token',
      'sb-auth-token'
    ];

    let sessionData = null;
    let accessToken = null;
    let usedKey = null;

    for (const key of possibleKeys) {
      const stored = localStorage.getItem(key);
      if (stored) {
        console.log(`Found session data in key: ${key}`);
        try {
          sessionData = JSON.parse(stored);
          if (sessionData.access_token) {
            accessToken = sessionData.access_token;
            usedKey = key;
            break;
          }
        } catch (e) {
          console.log(`Failed to parse data from key ${key}:`, e);
        }
      }
    }

    if (!accessToken) {
      console.log('No valid access token found in localStorage');
      console.log('Trying to get session from Supabase client...');

      // Try to get session from Supabase client if available
      if (window.supabase) {
        const { data: { session } } = await window.supabase.auth.getSession();
        if (session?.access_token) {
          accessToken = session.access_token;
          sessionData = session;
          console.log('Got session from Supabase client');
        }
      }
    }

    if (!accessToken) {
      console.error('No access token available for testing');
      return;
    }

    console.log('=== TOKEN ANALYSIS ===');
    console.log('Token source:', usedKey || 'Supabase client');
    console.log('Token length:', accessToken.length);
    console.log('Token starts with:', accessToken.substring(0, 20) + '...');
    console.log('Token ends with:', '...' + accessToken.substring(accessToken.length - 20));

    // Decode and analyze the token
    const decoded = decodeJWT(accessToken);
    if (decoded) {
      console.log('Token issuer:', decoded.payload.iss);
      console.log('Token subject:', decoded.payload.sub);
      console.log('Token audience:', decoded.payload.aud);
      console.log('Token expires:', new Date(decoded.payload.exp * 1000).toISOString());
      console.log('Token issued:', new Date(decoded.payload.iat * 1000).toISOString());
      console.log('Time until expiry:', Math.round((decoded.payload.exp * 1000 - Date.now()) / 1000), 'seconds');

      if (decoded.payload.exp * 1000 < Date.now()) {
        console.warn('⚠️ TOKEN IS EXPIRED!');
      }
    }

    console.log('=== TESTING BACKEND API CALL ===');
    console.log('Making request to: https://ai-nav.onrender.com/auth/sync-profile');

    const response = await fetch('https://ai-nav.onrender.com/auth/sync-profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('Response body:', responseText);

    if (!response.ok) {
      console.error('❌ API call failed');
      try {
        const errorData = JSON.parse(responseText);
        console.error('Error details:', errorData);

        // Specific analysis for 401 errors
        if (response.status === 401) {
          console.log('=== 401 UNAUTHORIZED ANALYSIS ===');
          console.log('This suggests the JWT token is not being accepted by the backend');
          console.log('Possible causes:');
          console.log('1. Token is expired (check expiry above)');
          console.log('2. Token signature validation failed');
          console.log('3. Backend JWT secret mismatch');
          console.log('4. Token format is incorrect');
          console.log('5. Backend is not extracting the token properly');
        }
      } catch (e) {
        console.error('Could not parse error response as JSON');
      }
    } else {
      console.log('✅ API call successful!');
      try {
        const data = JSON.parse(responseText);
        console.log('Response data:', data);
      } catch (e) {
        console.log('Response is not JSON');
      }
    }

  } catch (error) {
    console.error('Error testing backend call:', error);
  }
}

// Function to test with fresh session
async function testWithFreshSession() {
  console.log('=== TESTING WITH FRESH SESSION ===');

  if (typeof window === 'undefined' || !window.location) {
    console.log('This must be run in browser');
    return;
  }

  try {
    // Try to get fresh session from Supabase client
    const { createSupabaseBrowserClient } = await import('/src/lib/supabase/client.js');
    const supabase = createSupabaseBrowserClient();

    console.log('Getting fresh session from Supabase...');
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Error getting session:', error);
      return;
    }

    if (!session) {
      console.log('No active session found');
      return;
    }

    console.log('Fresh session obtained');
    console.log('Session user:', session.user?.email);
    console.log('Access token length:', session.access_token?.length);

    // Test with this fresh token
    const response = await fetch('https://ai-nav.onrender.com/auth/sync-profile', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
    });

    console.log('Fresh token test - Status:', response.status);
    const responseText = await response.text();
    console.log('Fresh token test - Response:', responseText);

  } catch (error) {
    console.error('Error in fresh session test:', error);
  }
}

// Function to compare localStorage vs Supabase client session
async function compareSessionSources() {
  console.log('=== COMPARING SESSION SOURCES ===');

  // Get from localStorage
  const localStorageSession = localStorage.getItem('sb-qpkllizwchhppvqsnagl-auth-token');
  let localToken = null;
  if (localStorageSession) {
    try {
      const parsed = JSON.parse(localStorageSession);
      localToken = parsed.access_token;
      console.log('localStorage token length:', localToken?.length);
      console.log('localStorage token preview:', localToken?.substring(0, 50) + '...');
    } catch (e) {
      console.log('Failed to parse localStorage session');
    }
  }

  // Get from Supabase client
  try {
    const { createSupabaseBrowserClient } = await import('/src/lib/supabase/client.js');
    const supabase = createSupabaseBrowserClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (session?.access_token) {
      console.log('Supabase client token length:', session.access_token.length);
      console.log('Supabase client token preview:', session.access_token.substring(0, 50) + '...');

      if (localToken && localToken !== session.access_token) {
        console.warn('⚠️ TOKENS ARE DIFFERENT!');
        console.log('This could explain the authentication issue');
      } else if (localToken === session.access_token) {
        console.log('✅ Tokens match between localStorage and Supabase client');
      }
    }
  } catch (error) {
    console.error('Error getting session from Supabase client:', error);
  }
}

// Instructions
console.log(`
=== JWT DEBUG SCRIPT ===

To use this script:
1. Open browser console on your AI Navigator site
2. Make sure you're logged in with Google
3. Run: analyzeCurrentToken()
4. Run: testBackendCall()
5. Run: testWithFreshSession()
6. Run: compareSessionSources()

Available functions:
- analyzeCurrentToken() - Analyze your current JWT token
- testBackendCall() - Test the backend API call with detailed debugging
- testWithFreshSession() - Test with a fresh session from Supabase
- compareSessionSources() - Compare localStorage vs Supabase client tokens
- decodeJWT(token) - Decode any JWT token
`);

// Auto-run if in browser
if (typeof window !== 'undefined') {
  console.log('JWT Debug script loaded. Run testBackendCall() to start comprehensive debugging.');
}

"use client"; // Add this directive for client-side hooks

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
// import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"; // OLD
import { createSupabaseBrowserClient } from '@/lib/supabase/client'; // NEW
import type { SupabaseClient, Session, AuthChangeEvent } from '@supabase/supabase-js';
// import { useRouter } from 'next/navigation'; // Not directly used, router.push handled in components
import { syncUserProfileAPI, addUpvote, removeUpvote, getUpvotedEntityIds } from '@/services/api'; // Adjust path
import { PrismaUserProfile } from '@/types/user'; // Import from local type definition
import { UserRole, UserStatus } from '@/types/enums';

export interface AuthContextType {
  supabase: SupabaseClient; // Use generic SupabaseClient or your specific Database type
  session: Session | null;
  user: PrismaUserProfile | null; // Changed back to PrismaUserProfile
  isLoading: boolean;
  setSession: (session: Session | null) => void; // Allow setting session directly
  authError: string | null; // Added for auth errors
  setAuthError: (error: string | null) => void; // Added for setting auth errors
  logout: () => Promise<void>;
  // Upvote state management
  upvotedEntityIds: Set<string>;
  isLoadingUpvotedIds: boolean;
  handleUpvote: (entityId: string) => Promise<void>;
  handleRemoveUpvote: (entityId: string) => Promise<void>;
  fetchUpvotedIds: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  console.log('[AuthContext] AuthProvider component initializing...');
  // const router = useRouter(); // Not directly used here
  const supabase = createSupabaseBrowserClient(); // Simplified - remove useMemo for now


  const [user, setUser] = useState<PrismaUserProfile | null>(null); // Changed back to PrismaUserProfile
  const [session, setSessionState] = useState<Session | null>(null); // This is the session we want to pass
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null); // Added for auth errors
  const [isHydrated, setIsHydrated] = useState(false); // Track hydration state
  const [initialAuthCheck, setInitialAuthCheck] = useState(false); // Track if initial auth check is done
  const [upvotedEntityIds, setUpvotedEntityIds] = useState<Set<string>>(new Set()); // Track upvoted entities
  const [isLoadingUpvotedIds, setIsLoadingUpvotedIds] = useState(false); // Track loading state for upvoted IDs

  // console.log("[AuthContext] AuthProvider rendering. Loading:", isLoading, "User:", !!user, "Session:", !!session); // Reduced verbosity

  // Upvote management functions - defined early to avoid circular dependencies
  const fetchUpvotedIds = useCallback(async () => {
    if (!session?.access_token) {
      console.log('[AuthContext] No session token, skipping upvoted IDs fetch');
      setUpvotedEntityIds(new Set()); // Clear upvoted IDs when no session
      return;
    }

    setIsLoadingUpvotedIds(true);

    try {
      console.log('[AuthContext] Fetching upvoted entity IDs...');
      const upvotedIds = await getUpvotedEntityIds();
      setUpvotedEntityIds(new Set(upvotedIds));
      console.log(`[AuthContext] Successfully fetched ${upvotedIds.length} upvoted entity IDs`);
    } catch (error) {
      console.error('[AuthContext] Error fetching upvoted IDs:', error);

      // Handle authentication errors by clearing upvoted IDs
      if (error instanceof Error && error.message.includes('Authentication required')) {
        console.log('[AuthContext] Authentication error, clearing upvoted IDs');
        setUpvotedEntityIds(new Set());
      }

      // Don't throw the error to prevent breaking the auth flow
    } finally {
      setIsLoadingUpvotedIds(false);
    }
  }, [session?.access_token]);

  const handleSyncProfile = useCallback(async (currentSession: Session | null) => {
    // console.log('[AuthContext] handleSyncProfile called with session:', currentSession ? "Exists" : "null"); // Keep if essential for debugging sync issues
    if (!currentSession) { // Early exit if no session
      // console.log('[AuthContext] handleSyncProfile: No session, setting user to null and stopping load.'); // Keep if essential
      setUser(null);
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    try {
      const backendProfile = await syncUserProfileAPI(currentSession);
      // console.log('[AuthContext] Profile synced:', backendProfile); // Keep if essential
      setUser(backendProfile);
      setAuthError(null);

      // Fetch upvoted entity IDs after successful profile sync
      fetchUpvotedIds();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('[AuthContext] Error syncing profile:', errorMessage);

      // If sync fails, create a basic user profile from session data instead of failing completely
      if (currentSession.user) {
        console.log('[AuthContext] Profile sync failed, using session data as fallback');
        const fallbackUser: PrismaUserProfile = {
          id: currentSession.user.id,
          authUserId: currentSession.user.id,
          email: currentSession.user.email || '<EMAIL>',
          displayName: currentSession.user.user_metadata?.display_name ||
                       currentSession.user.user_metadata?.displayName ||
                       currentSession.user.email?.split('@')[0] || null,
          username: currentSession.user.user_metadata?.username || null,
          bio: null,
          technicalLevel: null,
          profilePictureUrl: currentSession.user.user_metadata?.avatar_url || null,
          socialLinks: {},
          role: UserRole.USER,
          status: UserStatus.ACTIVE,
          createdAt: currentSession.user.created_at || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          lastLogin: null,
        };
        setUser(fallbackUser);
        setAuthError(null); // Don't show error if we have a fallback
      } else {
        setAuthError(`Profile sync failed: ${errorMessage}`);
        setUser(null);
      }
    } finally {
      setIsLoading(false);
    }
  }, [fetchUpvotedIds]); // fetchUpvotedIds is needed for calling after successful sync

  // Hydration effect
  useEffect(() => {
    console.log('[AuthContext] Hydration effect running, setting isHydrated to true');
    setIsHydrated(true);

    // PRIORITY 3 FIX: Check for session_refreshed parameter to trigger faster sync
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('session_refreshed') === 'true') {
        console.log('[AuthContext] Detected session_refreshed parameter, triggering immediate session refresh');

        // Clean up the URL parameter
        urlParams.delete('session_refreshed');
        const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
        window.history.replaceState({}, '', newUrl);

        // Trigger immediate session refresh
        setTimeout(() => {
          console.log('[AuthContext] Executing immediate session refresh due to OAuth callback');
          supabase.auth.getSession().then(({ data: { session } }) => {
            if (session) {
              console.log('[AuthContext] Session found after OAuth callback, syncing profile');
              setSessionState(session);
              handleSyncProfile(session);
            }
          });
        }, 100);
      }
    }


  }, [handleSyncProfile, supabase.auth]);

  useEffect(() => {
    // Don't start auth flow until hydrated
    console.log('[AuthContext] Main useEffect running, isHydrated:', isHydrated);
    if (!isHydrated) {
      console.log('[AuthContext] Not hydrated yet, returning early');
      return;
    }

    console.log('[AuthContext] Setting up onAuthStateChange listener...');

    const getInitialSession = async () => {
      console.log('[AuthContext] getInitialSession() called');
      const { data: { session: initialSession }, error: initialSessionError } = await supabase.auth.getSession();

      if (initialSessionError) {
        console.error('[AuthContext] Error getting initial session:', initialSessionError.message);
        setAuthError(`Failed to get initial session: ${initialSessionError.message}`);
        setIsLoading(false);
        setInitialAuthCheck(true);
        return;
      }

      console.log('[AuthContext] Initial getSession() result:', initialSession ? "Session exists" : "No session");
      if (initialSession) {
        console.log('[AuthContext] Session details - User ID:', initialSession.user?.id, 'Access token exists:', !!initialSession.access_token);
      }
      setSessionState(initialSession); // Set session state

      if (!initialSession) {
          console.log('[AuthContext] No initial session, setting isLoading to false and user to null.');
          setUser(null);
          setIsLoading(false);
          setInitialAuthCheck(true);
      } else {
          // If we have a session, sync the profile
          console.log('[AuthContext] Session found, attempting to sync profile...');
          await handleSyncProfile(initialSession);
          setInitialAuthCheck(true);
      }
      // If initialSession exists, onAuthStateChange may fire with INITIAL_SESSION (or SIGNED_IN if recently logged in)
      // which will then trigger handleSyncProfile. If not, loading is set to false above.
    };

    getInitialSession();

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, newSession: Session | null) => {
        // Skip initial session event if we already handled it
        if (event === 'INITIAL_SESSION' && initialAuthCheck) {
          return;
        }

        console.log(`[AuthContext] onAuthStateChange event: ${event}`, newSession ? "session data exists" : "no session data");
        setSessionState(newSession);

        const isSyncEvent =
          (event === 'SIGNED_IN') ||
          (event === 'TOKEN_REFRESHED') ||
          (event === 'USER_UPDATED' && !!newSession?.user);

        if (isSyncEvent) {
            await handleSyncProfile(newSession);
        } else if (event === 'SIGNED_OUT' || (event as string) === 'USER_DELETED') {
          setUser(null);
          setIsLoading(false);
        } else if (event === 'PASSWORD_RECOVERY' || event === 'MFA_CHALLENGE_VERIFIED'){
            setIsLoading(false);
        } else if (!newSession && event !== 'INITIAL_SESSION') {
            setUser(null);
            setIsLoading(false);
        }

        if (typeof window !== 'undefined' && (window.location.hash.includes('access_token') || window.location.hash.includes('error_description'))) {
          window.history.replaceState({}, document.title, window.location.pathname + window.location.search);
        }
      }
    );

    return () => {
      // console.log('[AuthContext] Unsubscribing from auth state changes.'); // Keep if essential
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [supabase, handleSyncProfile, isHydrated, initialAuthCheck]); // handleSyncProfile is memoized, supabase is stable

  const logout = useCallback(async () => {
    // console.log('[AuthContext] logout() called'); // Keep if essential
    setIsLoading(true); // Indicate loading during logout process
    setAuthError(null);
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("[AuthContext] Error logging out:", error.message);
      setAuthError(error.message);
      // If signOut itself fails, we might not get a SIGNED_OUT event, so manually stop loading.
      setIsLoading(false);
    } else {
      // Clear upvoted entities and loading state on successful logout
      setUpvotedEntityIds(new Set());
      setIsLoadingUpvotedIds(false);
    }
    // onAuthStateChange will handle setting user/session to null and potentially setIsLoading(false) via 'SIGNED_OUT' event.
    // However, setting isLoading(false) in case of error provides immediate feedback.
  }, [supabase]);

  // Additional upvote management functions
  const handleUpvote = useCallback(async (entityId: string) => {
    if (!session?.access_token) {
      console.error('[AuthContext] No session token for upvote');
      throw new Error('Authentication required');
    }

    // Optimistic update
    setUpvotedEntityIds(prev => new Set([...prev, entityId]));

    try {
      await addUpvote(entityId);
      console.log(`[AuthContext] Successfully upvoted entity ${entityId}`);
    } catch (error) {
      console.error(`[AuthContext] Failed to upvote entity ${entityId}:`, error);
      // Revert optimistic update on error
      setUpvotedEntityIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(entityId);
        return newSet;
      });
      throw error;
    }
  }, [session?.access_token]);

  const handleRemoveUpvote = useCallback(async (entityId: string) => {
    if (!session?.access_token) {
      console.error('[AuthContext] No session token for upvote removal');
      throw new Error('Authentication required');
    }

    // Optimistic update
    setUpvotedEntityIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(entityId);
      return newSet;
    });

    try {
      await removeUpvote(entityId);
      console.log(`[AuthContext] Successfully removed upvote for entity ${entityId}`);
    } catch (error) {
      console.error(`[AuthContext] Failed to remove upvote for entity ${entityId}:`, error);
      // Revert optimistic update on error
      setUpvotedEntityIds(prev => new Set([...prev, entityId]));
      throw error;
    }
  }, [session?.access_token]);

  const setSessionCallback = useCallback((newSession: Session | null) => {
    setSessionState(newSession);
    if (newSession?.user) {
         setUser({
            // Attempt to match PrismaUserProfile structure more closely from Supabase user object
            // Note: This is a *constructed* profile. Full data comes from syncUserProfileAPI.
            id: newSession.user.id, // This is Supabase User UUID, map to Prisma id if it's the same key
            authUserId: newSession.user.id, // Explicitly from Supabase User
            email: newSession.user.email || '<EMAIL>',
            displayName: newSession.user.user_metadata?.display_name || newSession.user.user_metadata?.displayName || newSession.user.email?.split('@')[0] || null,
            username: newSession.user.user_metadata?.username || null,
            role: (newSession.user.app_metadata?.userrole as PrismaUserProfile['role']) || UserRole.USER,
            profilePictureUrl: newSession.user.user_metadata?.avatar_url || newSession.user.user_metadata?.avatarUrl || null,
            bio: newSession.user.user_metadata?.bio || null,
            location: newSession.user.user_metadata?.location || null,
            websiteUrl: newSession.user.user_metadata?.website_url || newSession.user.user_metadata?.websiteUrl || null,
            githubUrl: newSession.user.user_metadata?.github_url || newSession.user.user_metadata?.githubUrl || null,
            linkedinUrl: newSession.user.user_metadata?.linkedin_url || newSession.user.user_metadata?.linkedinUrl || null,
            status: newSession.user.email_confirmed_at ? 'ACTIVE' : 'PENDING', // Example mapping
            // Fields not typically in Supabase user object, set to default/null
            technicalLevel: null,
            learningGoals: null,
            preferredContentTypes: null,
            hoursPerWeek: null,
            followedTags: [],
            followedCategories: [],
            notificationPreferencesId: null, // Cannot derive this from Supabase user alone
            notificationPreferences: null, // Cannot derive this from Supabase user alone
            createdAt: new Date(newSession.user.created_at),
            updatedAt: new Date(newSession.user.updated_at || newSession.user.created_at),
         } as unknown as PrismaUserProfile); // Reverted to 'as unknown as PrismaUserProfile' for pragmatic type assertion
    } else {
        setUser(null);
    }
    setIsLoading(false);
    if (newSession) setAuthError(null);
  }, []);

  const value = {
    supabase,
    session,
    user,
    isLoading,
    setSession: setSessionCallback,
    authError,
    setAuthError,
    logout,
    upvotedEntityIds,
    isLoadingUpvotedIds,
    handleUpvote,
    handleRemoveUpvote,
    fetchUpvotedIds,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 
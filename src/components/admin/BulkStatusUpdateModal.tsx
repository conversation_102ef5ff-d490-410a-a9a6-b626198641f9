"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, X, CheckCircle } from 'lucide-react';
import { adminUpdateEntityStatus } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface BulkStatusUpdateModalProps {
  entityIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedIds: string[], newStatus: string) => void;
}

const statusOptions = ['ACTIVE', 'PENDING', 'REJECTED', 'INACTIVE', 'ARCHIVED', 'NEEDS_REVISION'];

export const BulkStatusUpdateModal: React.FC<BulkStatusUpdateModalProps> = ({
  entityIds,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { session } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newStatus, setNewStatus] = useState<string>('');

  const handleUpdate = async () => {
    if (!session?.access_token) {
      setError('Authentication required');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log(`Bulk updating ${entityIds.length} entities to ${newStatus}`);

      // Update entities individually since bulk endpoint doesn't exist
      const updatePromises = entityIds.map(entityId =>
        adminUpdateEntityStatus(entityId, newStatus, session.access_token)
      );

      await Promise.all(updatePromises);

      // Call success callback
      onSuccess(entityIds, newStatus);
      onClose();
    } catch (error) {
      console.error('Error bulk updating entity status:', error);
      setError(error instanceof Error ? error.message : 'Failed to update entity status');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setNewStatus('');
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-blue-600" />
            Bulk Status Update
          </DialogTitle>
          <DialogDescription>
            Update the status for {entityIds.length} selected entities.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <p>
            You are about to change the status for <strong>{entityIds.length}</strong> entities.
            Please select the new status below.
          </p>

          <Select value={newStatus} onValueChange={setNewStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Select new status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleUpdate}
            disabled={loading || !newStatus}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
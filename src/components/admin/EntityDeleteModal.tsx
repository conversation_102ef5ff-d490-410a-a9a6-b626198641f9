"use client";

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Trash2, AlertTriangle, X } from 'lucide-react';
import { Entity } from '@/types/entity';
import { adminDeleteEntity, adminDeleteMultipleEntities } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface EntityDeleteModalProps {
  entities: Entity[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (deletedIds: string[]) => void;
}

export const EntityDeleteModal: React.FC<EntityDeleteModalProps> = ({
  entities,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { session } = useAuth();
  const [loading, setLoading] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');
  const [error, setError] = useState<string | null>(null);

  const isBulkDelete = entities.length > 1;
  const expectedConfirmationText = isBulkDelete
    ? `DELETE ${entities.length} ENTITIES`
    : entities.length === 1 ? `DELETE ${entities[0].name}` : '';

  const isConfirmationValid = confirmationText.trim() === expectedConfirmationText;

  useEffect(() => {
    if (isOpen) {
      setConfirmationText('');
      setError(null);
    }
  }, [isOpen]);

  const handleDelete = async () => {
    if (entities.length === 0 || !session?.access_token) {
      setError('Missing entities or authentication token');
      return;
    }

    if (!isConfirmationValid) {
      setError(`Please type exactly: ${expectedConfirmationText}`);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const entityIds = entities.map(e => e.id);

      if (isBulkDelete) {
        await adminDeleteMultipleEntities(entityIds, session.access_token);
      } else {
        await adminDeleteEntity(entityIds[0], session.access_token);
      }

      onSuccess(entityIds);
      onClose();
    } catch (error: any) {
      console.error('[DeleteModal] Error deleting entity:', error);
      setError(error.message || 'Failed to delete entity. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  if (entities.length === 0 || !isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center text-red-600">
            <AlertTriangle className="w-5 h-5 mr-2" />
            {isBulkDelete ? `Delete ${entities.length} Entities` : 'Delete Entity'}
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the {isBulkDelete ? 'selected entities' : 'entity'} and all associated data.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Warning:</strong> Deleting {isBulkDelete ? `${entities.length} entities` : 'this entity'} is permanent.
            </AlertDescription>
          </Alert>

          <div className="bg-gray-50 p-4 rounded-lg max-h-48 overflow-y-auto">
            <h4 className="font-medium text-gray-900 mb-2">
              {isBulkDelete ? 'Entities to be deleted:' : 'Entity to be deleted:'}
            </h4>
            {entities.map(e => (
              <div key={e.id} className="flex items-center space-x-3 mb-2">
                {e.logoUrl && (
                  <img
                    src={e.logoUrl}
                    alt={e.name}
                    className="w-8 h-8 rounded-md object-cover"
                  />
                )}
                <div>
                  <p className="font-medium text-gray-800 text-sm">{e.name}</p>
                  <p className="text-xs text-gray-500">{e.entityType.name}</p>
                </div>
              </div>
            ))}
          </div>

          <div>
            <Label htmlFor="confirmation">
              Type <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">
                {expectedConfirmationText}
              </code> to confirm deletion:
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={expectedConfirmationText}
              className="mt-1"
              disabled={loading}
            />
          </div>

          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            <X className="w-4 h-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={loading || !isConfirmationValid}
          >
            {loading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4 mr-2" />
            )}
            {isBulkDelete ? `Delete ${entities.length} Entities` : 'Delete Entity'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

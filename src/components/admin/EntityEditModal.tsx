"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Save, X } from 'lucide-react';
import { Entity, UpdateEntityDto, EntityType, Category, Tag, Feature } from '@/types/entity';
import { adminUpdateEntity, getEntityTypes, getCategories, getTags, getFeatures } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

// Validation schema for entity updates - comprehensive schema matching API
const updateEntitySchema = z.object({
  // Core entity information
  name: z.string().min(1, 'Name is required').optional(),
  short_description: z.string().optional(),
  description: z.string().optional(),
  website_url: z.string().url('Invalid URL').optional(),
  logo_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  documentation_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  contact_url: z.string().optional().or(z.literal('')),
  privacy_policy_url: z.string().url('Invalid URL').optional().or(z.literal('')),
  founded_year: z.number().min(1800).max(new Date().getFullYear()).optional().or(z.literal(null)),

  // Entity type and categorization
  entity_type_id: z.string().optional(),
  category_ids: z.array(z.string()).optional(),
  tag_ids: z.array(z.string()).optional(),
  feature_ids: z.array(z.string()).optional(),

  // SEO metadata
  meta_title: z.string().optional(),
  meta_description: z.string().optional(),

  // Company/organization details
  employee_count_range: z.enum(['C1_10', 'C11_50', 'C51_200', 'C201_500', 'C501_1000', 'C1001_5000', 'C5001_PLUS']).optional(),
  funding_stage: z.enum(['SEED', 'PRE_SEED', 'SERIES_A', 'SERIES_B', 'SERIES_C', 'SERIES_D_PLUS', 'PUBLIC']).optional(),
  location_summary: z.string().optional(),

  // Referral and affiliate
  ref_link: z.string().url('Invalid URL').optional().or(z.literal('')),
  affiliate_status: z.enum(['NONE', 'APPLIED', 'APPROVED', 'REJECTED']).optional(),

  // Review and sentiment data
  scraped_review_sentiment_label: z.string().optional(),
  scraped_review_sentiment_score: z.number().min(0).max(1).optional(),
  scraped_review_count: z.number().min(0).optional(),

  // Status and features
  status: z.enum(['PENDING', 'ACTIVE', 'REJECTED', 'INACTIVE', 'ARCHIVED', 'NEEDS_REVISION']).optional(),
  has_live_chat: z.boolean().optional(),

  // Social links
  social_links: z.object({
    twitter: z.string().optional(),
    linkedin: z.string().optional(),
    facebook: z.string().optional(),
    instagram: z.string().optional(),
    youtube: z.string().optional(),
    github: z.string().optional(),
    discord: z.string().optional(),
    slack: z.string().optional(),
  }).optional(),
});

type UpdateEntityFormData = z.infer<typeof updateEntitySchema>;

interface EntityEditModalProps {
  entity: Entity | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (updatedEntity: Entity) => void;
}

export const EntityEditModal: React.FC<EntityEditModalProps> = ({
  entity,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { session } = useAuth();
  const [loading, setLoading] = useState(false);
  const [entityTypes, setEntityTypes] = useState<EntityType[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<UpdateEntityFormData>({
    resolver: zodResolver(updateEntitySchema),
  });

  // Load reference data
  useEffect(() => {
    const loadReferenceData = async () => {
      try {
        const [entityTypesData, categoriesData, tagsData, featuresData] = await Promise.all([
          getEntityTypes().catch(err => {
            console.error('Error loading entity types:', err);
            return [];
          }),
          getCategories().catch(err => {
            console.error('Error loading categories:', err);
            return [];
          }),
          getTags().catch(err => {
            console.error('Error loading tags:', err);
            return [];
          }),
          getFeatures().catch(err => {
            console.error('Error loading features:', err);
            return [];
          }),
        ]);
        setEntityTypes(entityTypesData);
        setCategories(categoriesData);
        setTags(tagsData);
        setFeatures(featuresData);
      } catch (error) {
        console.error('Error loading reference data:', error);
        // Don't throw error, just log it and continue with empty arrays
      }
    };

    if (isOpen) {
      loadReferenceData();
    }
  }, [isOpen]);

  // Populate form when entity changes
  useEffect(() => {
    if (entity && isOpen) {
      // Reset form with entity data
      reset({
        name: entity.name,
        short_description: entity.shortDescription || '',
        description: entity.description,
        website_url: entity.websiteUrl,
        logo_url: entity.logoUrl || '',
        documentation_url: entity.documentationUrl || '',
        contact_url: entity.contactUrl || '',
        privacy_policy_url: entity.privacyPolicyUrl || '',
        founded_year: entity.foundedYear,
        entity_type_id: entity.entityType.id,
        meta_title: entity.metaTitle || '',
        meta_description: entity.metaDescription || '',
        employee_count_range: (entity as any).employeeCountRange || '',
        funding_stage: (entity as any).fundingStage || '',
        location_summary: (entity as any).locationSummary || '',
        ref_link: (entity as any).refLink || '',
        affiliate_status: (entity as any).affiliateStatus || 'NONE',
        scraped_review_sentiment_label: (entity as any).scrapedReviewSentimentLabel || '',
        scraped_review_sentiment_score: (entity as any).scrapedReviewSentimentScore || undefined,
        scraped_review_count: (entity as any).scrapedReviewCount || undefined,
        status: entity.status as any,
        has_live_chat: (entity as any).hasLiveChat || false,
        social_links: {
          twitter: entity.socialLinks?.twitter || '',
          linkedin: entity.socialLinks?.linkedin || '',
          facebook: entity.socialLinks?.facebook || '',
          instagram: entity.socialLinks?.instagram || '',
          youtube: entity.socialLinks?.youtube || '',
          github: entity.socialLinks?.github || '',
          discord: entity.socialLinks?.discord || '',
          slack: entity.socialLinks?.slack || '',
        },
      });

      // Set selected arrays with safe null checks
      setSelectedCategories(Array.isArray(entity.categories) ? entity.categories.map(c => c.id) : []);
      setSelectedTags(Array.isArray(entity.tags) ? entity.tags.map(t => t.id) : []);
      setSelectedFeatures(Array.isArray(entity.features) ? entity.features.map(f => f.id) : []);
    }
  }, [entity, isOpen, reset]);

  const onSubmit = async (data: UpdateEntityFormData) => {
    if (!entity || !session?.access_token) return;

    try {
      setLoading(true);

      // Prepare update payload
      const updatePayload: UpdateEntityDto = {
        ...data,
        category_ids: selectedCategories,
        tag_ids: selectedTags,
        feature_ids: selectedFeatures,
      };

      // Remove empty strings and convert to undefined, handle null values
      Object.keys(updatePayload).forEach(key => {
        const value = (updatePayload as any)[key];
        if (value === '' || value === null) {
          (updatePayload as any)[key] = undefined;
        }
      });

      const updatedEntity = await adminUpdateEntity(entity.id, updatePayload, session.access_token);
      onSuccess(updatedEntity);
      onClose();
    } catch (error: any) {
      console.error('Error updating entity:', error);
      const errorMessage = error?.message || 'Failed to update entity. Please try again.';
      alert(`Update failed: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      onClose();
    }
  };

  if (!entity) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Entity: {entity.name}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="categorization">Categories & Tags</TabsTrigger>
              <TabsTrigger value="metadata">Metadata & SEO</TabsTrigger>
              <TabsTrigger value="social">Social & Reviews</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Entity name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="website_url">Website URL *</Label>
                  <Input
                    id="website_url"
                    {...register('website_url')}
                    placeholder="https://example.com"
                  />
                  {errors.website_url && (
                    <p className="text-sm text-red-600 mt-1">{errors.website_url.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="logo_url">Logo URL</Label>
                  <Input
                    id="logo_url"
                    {...register('logo_url')}
                    placeholder="https://example.com/logo.png"
                  />
                  {errors.logo_url && (
                    <p className="text-sm text-red-600 mt-1">{errors.logo_url.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="founded_year">Founded Year</Label>
                  <Input
                    id="founded_year"
                    type="number"
                    {...register('founded_year', { valueAsNumber: true })}
                    placeholder="2023"
                  />
                  {errors.founded_year && (
                    <p className="text-sm text-red-600 mt-1">{errors.founded_year.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="short_description">Short Description</Label>
                <Textarea
                  id="short_description"
                  {...register('short_description')}
                  placeholder="Brief description..."
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="description">Full Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Detailed description..."
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="documentation_url">Documentation URL</Label>
                  <Input
                    id="documentation_url"
                    {...register('documentation_url')}
                    placeholder="https://docs.example.com"
                  />
                </div>

                <div>
                  <Label htmlFor="contact_url">Contact URL</Label>
                  <Input
                    id="contact_url"
                    {...register('contact_url')}
                    placeholder="https://example.com/contact"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="categorization" className="space-y-4">
              <div>
                <Label htmlFor="entity_type_id">Entity Type</Label>
                <Select
                  value={watch('entity_type_id')}
                  onValueChange={(value) => setValue('entity_type_id', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select entity type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.isArray(entityTypes) && entityTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Categories</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(categories) && categories.map((category) => (
                    <label key={category.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedCategories.includes(category.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCategories([...selectedCategories, category.id]);
                          } else {
                            setSelectedCategories(selectedCategories.filter(id => id !== category.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{category.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <Label>Tags</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(tags) && tags.map((tag) => (
                    <label key={tag.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedTags.includes(tag.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedTags([...selectedTags, tag.id]);
                          } else {
                            setSelectedTags(selectedTags.filter(id => id !== tag.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{tag.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <Label>Features</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto border rounded p-2">
                  {Array.isArray(features) && features.map((feature) => (
                    <label key={feature.id} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={selectedFeatures.includes(feature.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFeatures([...selectedFeatures, feature.id]);
                          } else {
                            setSelectedFeatures(selectedFeatures.filter(id => id !== feature.id));
                          }
                        }}
                        className="rounded"
                      />
                      <span>{feature.name}</span>
                    </label>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="metadata" className="space-y-4">
              <div>
                <Label htmlFor="meta_title">Meta Title (SEO)</Label>
                <Input
                  id="meta_title"
                  {...register('meta_title')}
                  placeholder="SEO title for search engines"
                />
              </div>

              <div>
                <Label htmlFor="meta_description">Meta Description (SEO)</Label>
                <Textarea
                  id="meta_description"
                  {...register('meta_description')}
                  placeholder="SEO description for search engines"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="employee_count_range">Employee Count Range</Label>
                  <Select
                    value={watch('employee_count_range')}
                    onValueChange={(value) => setValue('employee_count_range', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="C1_10">1-10</SelectItem>
                      <SelectItem value="C11_50">11-50</SelectItem>
                      <SelectItem value="C51_200">51-200</SelectItem>
                      <SelectItem value="C201_500">201-500</SelectItem>
                      <SelectItem value="C501_1000">501-1000</SelectItem>
                      <SelectItem value="C1001_5000">1001-5000</SelectItem>
                      <SelectItem value="C5001_PLUS">5000+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="funding_stage">Funding Stage</Label>
                  <Select
                    value={watch('funding_stage')}
                    onValueChange={(value) => setValue('funding_stage', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select stage" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SEED">Seed</SelectItem>
                      <SelectItem value="PRE_SEED">Pre-Seed</SelectItem>
                      <SelectItem value="SERIES_A">Series A</SelectItem>
                      <SelectItem value="SERIES_B">Series B</SelectItem>
                      <SelectItem value="SERIES_C">Series C</SelectItem>
                      <SelectItem value="SERIES_D_PLUS">Series D+</SelectItem>
                      <SelectItem value="PUBLIC">Public</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="location_summary">Location Summary</Label>
                <Input
                  id="location_summary"
                  {...register('location_summary')}
                  placeholder="San Francisco, Remote, Global"
                />
              </div>
            </TabsContent>

            <TabsContent value="social" className="space-y-4">
              <div>
                <Label>Social Links</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <Label htmlFor="social_twitter">Twitter Handle</Label>
                    <Input
                      id="social_twitter"
                      {...register('social_links.twitter')}
                      placeholder="@username"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_linkedin">LinkedIn</Label>
                    <Input
                      id="social_linkedin"
                      {...register('social_links.linkedin')}
                      placeholder="company/profile"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_github">GitHub</Label>
                    <Input
                      id="social_github"
                      {...register('social_links.github')}
                      placeholder="username or organization"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_discord">Discord</Label>
                    <Input
                      id="social_discord"
                      {...register('social_links.discord')}
                      placeholder="invite link or server"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_youtube">YouTube</Label>
                    <Input
                      id="social_youtube"
                      {...register('social_links.youtube')}
                      placeholder="channel name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="social_facebook">Facebook</Label>
                    <Input
                      id="social_facebook"
                      {...register('social_links.facebook')}
                      placeholder="page name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label>Review & Sentiment Data</Label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                  <div>
                    <Label htmlFor="scraped_review_count">Scraped Review Count</Label>
                    <Input
                      id="scraped_review_count"
                      type="number"
                      {...register('scraped_review_count', { valueAsNumber: true })}
                      placeholder="150"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scraped_review_sentiment_score">Sentiment Score (0-1)</Label>
                    <Input
                      id="scraped_review_sentiment_score"
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      {...register('scraped_review_sentiment_score', { valueAsNumber: true })}
                      placeholder="0.85"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scraped_review_sentiment_label">Sentiment Label</Label>
                    <Input
                      id="scraped_review_sentiment_label"
                      {...register('scraped_review_sentiment_label')}
                      placeholder="Positive"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch('status')}
                  onValueChange={(value) => setValue('status', value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="REJECTED">Rejected</SelectItem>
                    <SelectItem value="INACTIVE">Inactive</SelectItem>
                    <SelectItem value="ARCHIVED">Archived</SelectItem>
                    <SelectItem value="NEEDS_REVISION">Needs Revision</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ref_link">Referral/Affiliate Link</Label>
                  <Input
                    id="ref_link"
                    {...register('ref_link')}
                    placeholder="https://example.com?ref=partner"
                  />
                </div>

                <div>
                  <Label htmlFor="affiliate_status">Affiliate Status</Label>
                  <Select
                    value={watch('affiliate_status')}
                    onValueChange={(value) => setValue('affiliate_status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NONE">None</SelectItem>
                      <SelectItem value="APPLIED">Applied</SelectItem>
                      <SelectItem value="APPROVED">Approved</SelectItem>
                      <SelectItem value="REJECTED">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="privacy_policy_url">Privacy Policy URL</Label>
                <Input
                  id="privacy_policy_url"
                  {...register('privacy_policy_url')}
                  placeholder="https://example.com/privacy"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="has_live_chat"
                  {...register('has_live_chat')}
                  className="rounded"
                />
                <Label htmlFor="has_live_chat">Has Live Chat Support</Label>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting || loading}
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || loading}
            >
              {(isSubmitting || loading) ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Save Changes
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

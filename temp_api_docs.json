{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/healthz": {"get": {"operationId": "AppController_healthCheck", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/test-connection": {"get": {"operationId": "AppController_testConnection", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/auth/signup": {"post": {"description": "Creates a new user account. An email confirmation will be sent to the provided email address. The username and email must be unique.", "operationId": "AuthController_signUp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterAuthDto"}}}}, "responses": {"201": {"description": "User registered successfully. Please check your email to confirm your registration.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignUpResponseDto"}}}}, "400": {"description": "Invalid input data (e.g., email format, password strength, missing fields)."}, "409": {"description": "Email or username already exists."}, "429": {"description": "Too many signup attempts. Please try again later."}}, "summary": "Register a new user", "tags": ["Authentication"]}}, "/auth/login": {"post": {"description": "Authenticates a user and returns a session object including JWT access and refresh tokens.", "operationId": "AuthController_signIn", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginAuthDto"}}}}, "responses": {"200": {"description": "User logged in successfully.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseDto"}}}}, "400": {"description": "Invalid input data."}, "401": {"description": "Invalid credentials or email not confirmed."}, "429": {"description": "Too many login attempts. Please try again later."}}, "summary": "Log in an existing user", "tags": ["Authentication"]}}, "/auth/logout": {"post": {"description": "Invalidates the current user's session. Requires a valid JWT Bearer token.", "operationId": "AuthController_logout", "parameters": [], "responses": {"200": {"description": "User logged out successfully."}, "401": {"description": "User not authenticated or token invalid."}}, "security": [{"bearer": []}], "summary": "Log out the current user", "tags": ["Authentication"]}}, "/auth/forgot-password": {"post": {"description": "Sends a password reset link to the user's registered email address if the account exists.", "operationId": "AuthController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "If an account with this email exists, a password reset link has been sent."}, "400": {"description": "Invalid input data (e.g., email format)."}, "404": {"description": "Email not found (though the response is generic for security)."}, "429": {"description": "Too many password reset attempts. Please try again later."}}, "summary": "Request a password reset link", "tags": ["Authentication"]}}, "/auth/reset-password": {"post": {"description": "Allows a user to set a new password after following a password reset link sent to their email. The client should include the temporary access token (obtained from the password recovery URL fragment or specific auth event) as a Bearer token in the Authorization header for this request.", "operationId": "AuthController_resetPassword", "parameters": [{"name": "authorization", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "Password has been reset successfully."}, "400": {"description": "Invalid input data (e.g., passwords don't match, password too weak)."}, "401": {"description": "Invalid or expired password reset token."}}, "security": [{"bearer": []}], "summary": "Reset user password using a token from email", "tags": ["Authentication"]}}, "/auth/resend-confirmation": {"post": {"description": "Sends a new email confirmation link to the user's email address if their account exists and is pending confirmation.", "operationId": "AuthController_resendConfirmation", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendConfirmationDto"}}}}, "responses": {"200": {"description": "If your account exists and requires confirmation, a new confirmation email has been sent."}, "400": {"description": "Invalid input data (e.g., email format)."}, "409": {"description": "Email already confirmed or recent resend attempt."}, "429": {"description": "Too many resend attempts. Please try again later."}}, "summary": "Resend email confirmation link", "tags": ["Authentication"]}}, "/auth/sync-profile": {"post": {"operationId": "AuthController_syncProfile", "parameters": [], "responses": {"200": {"description": "Profile synced successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileMinimalDto"}}}}, "400": {"description": "Bad Request - Invalid user data"}, "401": {"description": "Unauthorized"}, "500": {"description": "Internal Server Error"}}, "security": [{"bearer": []}], "summary": "Synchronize authenticated user profile with local database", "tags": ["Authentication"]}}}}
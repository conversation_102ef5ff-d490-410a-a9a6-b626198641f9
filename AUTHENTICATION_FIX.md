# Authentication Fix: "Invalid Signature" Error

## Problem
When new users sign in with Google, they get an "invalid signature" error and the user profile sync fails with a 401 Unauthorized error from the backend.

## Root Cause
The backend (https://ai-nav.onrender.com) is not properly configured to validate JWT tokens issued by Supabase. The backend needs the Supabase JWT secret to verify token signatures.

## Solution Steps

### 1. Get the Supabase JWT Secret
1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/qpkllizwchhppvqsnagl/settings/api
2. Look for the "JWT Secret" in the Project API keys section
3. Copy the JWT Secret value

### 2. Configure Backend Environment Variables
The backend needs these environment variables:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://qpkllizwchhppvqsnagl.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFwa2xsaXp3Y2hocHB2cXNuYWdsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyMjAxODIsImV4cCI6MjA2MDc5NjE4Mn0.OcNyH3W8DZAtrb_-g2JLeNUkMSme6nySl2Xkgm447RA
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFwa2xsaXp3Y2hocHB2cXNuYWdsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTIyMDE4MiwiZXhwIjoyMDYwNzk2MTgyfQ.hS6smBHvNNkwt7i2HkBhYqU6nMD9LH3zl4D71NI_SM8

# JWT Secret for validating Supabase tokens
SUPABASE_JWT_SECRET=[GET_FROM_SUPABASE_DASHBOARD]
```

### 3. Backend JWT Configuration
The backend needs to be configured to use the Supabase JWT secret instead of a custom JWT_SECRET. 

**Current Issue**: The backend is likely using this custom JWT secret:
```bash
JWT_SECRET=QxaFTd1xj9LT5IjiniXDsFOFds77oFap+i9fJ8o8I+g4VC72FgspWll1uNUyCe+Kl1MyOcNnPPKC6ivl0L0YHg==
```

**Required Fix**: The backend should use `SUPABASE_JWT_SECRET` for validating tokens from Supabase.

### 4. Backend Code Changes Needed
The backend authentication guard/middleware should:

1. **Use Supabase JWT Secret**: Configure JWT validation to use `SUPABASE_JWT_SECRET`
2. **Validate Supabase Token Format**: Ensure it can handle Supabase's JWT token structure
3. **Extract User Info**: Properly extract user information from Supabase JWT payload

### 5. Verify Token Structure
Supabase JWT tokens have this structure:
```json
{
  "aud": "authenticated",
  "exp": **********,
  "iat": **********,
  "iss": "https://qpkllizwchhppvqsnagl.supabase.co/auth/v1",
  "sub": "user-uuid",
  "email": "<EMAIL>",
  "phone": "",
  "app_metadata": {
    "provider": "google",
    "providers": ["google"]
  },
  "user_metadata": {
    "avatar_url": "...",
    "email": "...",
    "email_verified": true,
    "full_name": "...",
    "iss": "...",
    "name": "...",
    "picture": "...",
    "provider_id": "...",
    "sub": "..."
  },
  "role": "authenticated",
  "aal": "aal1",
  "amr": [{"method": "oauth", "timestamp": **********}],
  "session_id": "session-uuid"
}
```

## Testing the Fix

### 1. Test Token Validation
You can test if the backend can validate tokens by:
```bash
curl -H "Authorization: Bearer YOUR_SUPABASE_TOKEN" \
     https://ai-nav.onrender.com/auth/sync-profile
```

### 2. Check Backend Logs
Look for JWT validation errors in the backend logs.

### 3. Test New User Registration
1. Clear browser data for your site
2. Sign in with Google using a different email
3. Check if the profile sync succeeds

## Immediate Workaround
The frontend already has a fallback mechanism in `AuthContext.tsx` that creates a basic user profile from session data if the backend sync fails. This prevents the app from breaking, but users won't have full backend functionality.

## Next Steps
1. Get the Supabase JWT secret from the dashboard
2. Configure the backend with the correct JWT secret
3. Update backend JWT validation logic if needed
4. Test with a new Google sign-in
5. Verify that users are properly created in the database

## Related Files
- Frontend: `src/contexts/AuthContext.tsx` (lines 82-133)
- Frontend: `src/services/api.ts` (lines 144-177)
- Backend: Authentication middleware/guards (needs JWT secret update)
